#ifndef CONFIG_H
#define CONFIG_H

#include <stdint.h> // 添加uint8_t类型定义

/* ------------------------------------------------------------------------
 * 基础屏幕参数配置
 * ------------------------------------------------------------------------ */
#define SCREEN_WIDTH 128                                   // 屏幕宽度（列数）
#define SCREEN_HEIGHT 32                                   // 屏幕高度（行数）
#define PANEL_CHAIN_LENGTH 1                               // 链式面板数量
#define TOTAL_LED_COUNT (SCREEN_WIDTH * SCREEN_HEIGHT * 3) // 总LED数量

/* ------------------------------------------------------------------------
 * 串口和蓝牙配置
 * ------------------------------------------------------------------------ */
#define SERIAL_BAUD_RATE 115200   // 串口波特率
#define BT_DEVICE_NAME "ESP32-21" // 蓝牙设备名称

/* ------------------------------------------------------------------------
 * HUB75 LED矩阵屏引脚配置
 * ------------------------------------------------------------------------ */
// // 数据引脚 (RGB1)
// #define PIN_R1 25 // 红色数据线1
// #define PIN_G1 26 // 绿色数据线1
// #define PIN_B1 27 // 蓝色数据线1

// // 数据引脚 (RGB2) - 用于下半部分扫描
// #define PIN_R2 14 // 红色数据线2
// #define PIN_G2 12 // 绿色数据线2
// #define PIN_B2 13 // 蓝色数据线2

// // 地址选择引脚
// #define PIN_A 23 // 地址线A
// #define PIN_B 22 // 地址线B
// #define PIN_C 21 // 地址线C
// #define PIN_D 19 // 地址线D
// #define PIN_E 18 // 地址线E (可选，用于更大的面板)

// // 控制引脚
// #define PIN_LAT 4  // 锁存信号 (Latch)
// #define PIN_OE 15  // 输出使能 (Output Enable，低电平有效)
// #define PIN_CLK 16 // 时钟信号 (Clock)

// 数据引脚 (RGB1)
#define PIN_R1 25    // 红色数据线1
#define PIN_G1 26    // 绿色数据线1
#define PIN_B1 27    // 蓝色数据线1

// 数据引脚 (RGB2) - 用于下半部分扫描
#define PIN_R2 14    // 红色数据线2
#define PIN_G2 17    // 绿色数据线2
#define PIN_B2 13    // 蓝色数据线2

// 地址选择引脚
#define PIN_A 32     // 地址线A
#define PIN_B 33     // 地址线B
#define PIN_C 21      // 地址线C
#define PIN_D 15     // 地址线D
#define PIN_E 18     // 地址线E (可选，用于更大的面板)

// 控制引脚
#define PIN_LAT 4    // 锁存信号 (Latch)
#define PIN_OE 2    // 输出使能 (Output Enable，低电平有效)
#define PIN_CLK 16   // 时钟信号 (Clock)

/* ------------------------------------------------------------------------
 * LED矩阵屏驱动配置
 * ------------------------------------------------------------------------ */
#define MATRIX_BRIGHTNESS 50       // 默认亮度 (0-255)
#define MATRIX_COLOR_DEPTH 8       // 颜色深度 (位)
#define MATRIX_REFRESH_RATE 120    // 刷新率 (Hz)
#define MATRIX_DRIVER_CHIP FM6126A // 驱动芯片类型

/* ------------------------------------------------------------------------
 * 帧头尾配置
 * ------------------------------------------------------------------------ */
#define BT_FRAME_HEADER_1 0xAA // 帧头第1字节
#define BT_FRAME_HEADER_2 0x55 // 帧头第2字节
#define BT_FRAME_TAIL_1 0x0D   // 帧尾第1字节
#define BT_FRAME_TAIL_2 0x0A   // 帧尾第2字节

/* ------------------------------------------------------------------------
 * 蓝牙命令配置
 * ------------------------------------------------------------------------ */
#define BT_CMD_SET_DIRECTION 0x00      // 设置文本显示方向命令（默认：正向显示）
#define BT_CMD_SET_VERTICAL 0x01       // 设置文本竖向显示命令
#define BT_CMD_SET_FONT_16x16 0x02     // 设置16x16字体命令
#define BT_CMD_SET_FONT_32x32 0x03     // 设置32x32字体命令
#define BT_CMD_SET_TEXT 0x04           // 设置文本命令
#define BT_CMD_SET_ANIMATION 0x05      // 设置动画命令
#define BT_CMD_SET_COLOR 0x06          // 设置颜色命令
#define BT_CMD_SET_BRIGHTNESS 0x07     // 设置亮度命令
#define BT_CMD_SET_EFFECT 0x08         // 设置特效命令
#define BT_CMD_SET_BORDER 0x09         // 设置边框命令
#define BT_CMD_SET_SPECIFIC_COLOR 0x0A // 设置特定文本颜色命令
#define BT_CMD_SET_RANDOM_COLOR 0x0B   // 设置文本随机颜色命令
#define BT_CMD_SET_SPLIT_MODE 0x0C     // 设置分区显示模式命令
#define BT_CMD_FILE_START 0x10         // 文件传输开始命令
#define BT_CMD_FILE_DATA 0x11          // 文件数据传输命令
#define BT_CMD_FILE_END 0x12           // 文件传输结束命令
#define BT_CMD_FILE_LIST 0x13          // 获取文件列表命令
#define BT_CMD_FILE_DELETE 0x14        // 删除文件命令
#define BT_CMD_PLAY_GIF 0x15           // 播放指定GIF命令
#define BT_CMD_CLOCK_MODE 0x20         // 时钟模式命令
#define BT_CMD_SCORE_MODE 0x21         // 计分模式命令
#define BT_CMD_LIST_START 0x30         // 列表开始
#define BT_CMD_LIST_DATA 0x31          // 列表命令
#define BT_CMD_LIST_END 0x32           // 列表结束
#define BT_CMD_PLAY_GIF_OVERLAY 0x16   // 叠层模式播放GIF命令
#define BT_CMD_PLAY_GIF_SPECIAL 0x17   // 创意文本模式播放GIF命令
#define BT_CMD_SET_EDIT_INDEX 0x40     // 设置列表编辑索引命令
#define BT_CMD_SAVE_TO_LIST 0x41       // 保存当前配置到列表并切换下一索引命令
#define BT_CMD_EDIT_MODE_SWITCH 0x42   // 列表编辑模式开关命令
#define BT_CMD_LIST_PLAY_START 0x43    // 开始列表播放命令
#define BT_CMD_LIST_PLAY_STOP 0x44     // 停止列表播放命令
#define BT_CMD_LIST_CLEAR 0x45         // 清空列表命令
#define BT_CMD_LIST_DELETE_ITEM 0x46   // 删除列表项命令
#define BT_CMD_CANCEL_EDIT 0x47        // 取消编辑并清空临时配置命令
/* ------------------------------------------------------------------------
 * 性能配置
 * ------------------------------------------------------------------------ */
#define BT_MAX_FRAME_SIZE 8192   // 最大帧大小
#define BT_FRAME_TIMEOUT_MS 5000 // 帧超时时间（毫秒）
#define BT_MAX_ERROR_COUNT 100   // 最大错误计数

/* ------------------------------------------------------------------------
 * 系统配置
 * ------------------------------------------------------------------------ */
#define SYSTEM_STARTUP_DELAY 1000 // 系统启动延迟 (毫秒)
#define WATCHDOG_TIMEOUT 30000    // 看门狗超时时间 (毫秒)

/* ------------------------------------------------------------------------
 * 新增颜色系统参数定义
 * ------------------------------------------------------------------------ */
#define BT_SPECIFIC_COLOR_DATA_LEN 5 // 特定字符颜色命令数据长度（屏幕区域+字符索引+RGB，1+1+3字节）
#define BT_RANDOM_COLOR_DATA_LEN 4   // 随机颜色命令数据长度（屏幕区域+模式+间隔+种子，1+1+1+1字节）

// 颜色模式类型定义
#define COLOR_MODE_FIXED 0x00    // 固定色模式
#define COLOR_MODE_GRADIENT 0x01 // 渐变色模式
#define COLOR_MODE_SPECIFIC 0x02 // 特定字符颜色模式
#define COLOR_MODE_RANDOM 0x03   // 随机颜色模式

// 随机颜色模式定义
#define RANDOM_COLOR_OFF 0x00       // 关闭随机色
#define RANDOM_COLOR_EACH_CHAR 0x01 // 每个字符不同随机色
#define RANDOM_COLOR_ALL_SAME 0x02  // 所有字符相同随机色(定时变化)
#define RANDOM_COLOR_RAINBOW 0x03   // 彩虹色随机序列
#define RANDOM_COLOR_WARM 0x04      // 暖色系随机
#define RANDOM_COLOR_COOL 0x05      // 冷色系随机
#define RANDOM_COLOR_BRIGHT 0x06    // 高亮度随机色

/* ------------------------------------------------------------------------
 * 参数定义
 * ------------------------------------------------------------------------ */
#define BT_COLOR_TARGET_TEXT 0x01       // 选择文本
#define BT_COLOR_TARGET_BACKGROUND 0x02 // 选择背景
#define BT_COLOR_MODE_FIXED 0x01        // 固定色
#define BT_COLOR_MODE_GRADIENT 0x02     // 渐变色
#define BT_COLOR_DATA_LEN 7             // 颜色命令数据长度（屏幕区域+目标+模式+RGB+渐变模式，1+1+1+3+1字节）
#define BT_BRIGHTNESS_DATA_LEN 1        // 亮度命令数据长度（1字节）
#define BT_EFFECT_DATA_LEN 3            // 特效命令数据长度（3字节：屏幕区域+特效类型+速度）
#define BT_BORDER_DATA_LEN 4            // 边框命令数据长度（4字节：样式+颜色+效果+速度）

/* ------------------------------------------------------------------------
 * 特效类型定义
 * ------------------------------------------------------------------------ */
#define BT_EFFECT_FIXED 0x00        // 固定显示
#define BT_EFFECT_SCROLL_LEFT 0x01  // 左移滚动
#define BT_EFFECT_SCROLL_RIGHT 0x02 // 右移滚动
#define BT_EFFECT_BLINK 0x03        // 闪烁特效
#define BT_EFFECT_BREATHE 0x04      // 呼吸特效
#define BT_EFFECT_SCROLL_UP 0x07    // 向上滚动（竖向显示时的左滚动）
#define BT_EFFECT_SCROLL_DOWN 0x08  // 向下滚动（竖向显示时的右滚动）

/* ------------------------------------------------------------------------
 * 渐变色模式定义
 * ------------------------------------------------------------------------ */
#define BT_GRADIENT_FIXED 0x00         // 固定色（无渐变）
#define BT_GRADIENT_VERTICAL_1 0x01    // 上下渐变组合1
#define BT_GRADIENT_VERTICAL_2 0x02    // 上下渐变组合2
#define BT_GRADIENT_VERTICAL_3 0x03    // 上下渐变组合3
#define BT_GRADIENT_HORIZONTAL_1 0x04  // 左右渐变组合1
#define BT_GRADIENT_HORIZONTAL_2 0x05  // 左右渐变组合2
#define BT_GRADIENT_HORIZONTAL_3 0x06  // 左右渐变组合3
#define BT_GRADIENT_DIAGONAL_45_1 0x07 // 45度渐变组合1
#define BT_GRADIENT_DIAGONAL_45_2 0x08 // 45度渐变组合2
#define BT_GRADIENT_DIAGONAL_45_3 0x09 // 45度渐变组合3

/* ------------------------------------------------------------------------
 * 屏幕区域定义
 * ------------------------------------------------------------------------ */
#define BT_SCREEN_UPPER 0x01 // 上半屏
#define BT_SCREEN_LOWER 0x02 // 下半屏
#define BT_SCREEN_BOTH 0x03  // 全屏

/* ------------------------------------------------------------------------
 * 文本显示方向定义
 * ------------------------------------------------------------------------ */
#define BT_DIRECTION_HORIZONTAL 0x00 // 正向显示（默认）
#define BT_DIRECTION_VERTICAL 0x01   // 竖向显示（旋转90度）

/* ------------------------------------------------------------------------
 * 字体大小定义
 * ------------------------------------------------------------------------ */
#define BT_FONT_16x16 0x00 // 16x16字体（默认）
#define BT_FONT_32x32 0x01 // 32x32字体

/* ------------------------------------------------------------------------
 * 16x16字体参数
 * ------------------------------------------------------------------------ */
#define FONT_WIDTH_16 16   // 16x16字体宽度
#define FONT_HEIGHT_16 16  // 16x16字体高度
#define FONT_BYTES_16 32   // 16x16字体字节数
#define CHAR_SPACING_16 16 // 16x16字符间距

/* ------------------------------------------------------------------------
 * 32x32字体参数
 * ------------------------------------------------------------------------ */
#define FONT_WIDTH_32 32   // 32x32字体宽度
#define FONT_HEIGHT_32 32  // 32x32字体高度
#define FONT_BYTES_32 128  // 32x32字体字节数
#define CHAR_SPACING_32 32 // 32x32字符间距

/* ------------------------------------------------------------------------
 * 时钟模式和计分模式参数定义
 * ------------------------------------------------------------------------ */
// 时钟模式参数
#define BT_CLOCK_SCREEN_SMALL 0x00 // 小屏模式
#define BT_CLOCK_SCREEN_BIG 0x01   // 大屏模式
#define BT_CLOCK_LANG_CHINESE 0x00 // 中文
#define BT_CLOCK_LANG_ENGLISH 0x01 // 英文
#define BT_CLOCK_DATA_LEN 12       // 时钟模式数据长度 (1+1+3+3+1+1+1+1=12字节)

// 计分模式参数
#define BT_SCORE_REGION_LEFT_TEXT 0x00   // 左上文本区域
#define BT_SCORE_REGION_LEFT_SCORE 0x01  // 左下计分区域
#define BT_SCORE_REGION_RIGHT_TEXT 0x02  // 右上文本区域
#define BT_SCORE_REGION_RIGHT_SCORE 0x03 // 右下计分区域
#define BT_SCORE_TEXT_DATA_LEN_MIN 36    // 文本区域最小数据长度 (区域1字节+RGB3字节+点阵32字节，单字符)
#define BT_SCORE_TEXT_DATA_LEN_MAX 324   // 文本区域最大数据长度 (区域1字节+RGB3字节+点阵320字节，10字符)
#define BT_SCORE_MAX_CHARS 10            // 文本区域最大字符数
#define BT_SCORE_CHAR_BYTES 32           // 每个16×16字符的字节数
#define BT_SCORE_NUM_DATA_LEN 9          // 计分区域数据长度 (区域1字节+颜色3字节+初值2字节+操作1字节+数值2字节)

/* ------------------------------------------------------------------------
 * 错误协议定义
 * ------------------------------------------------------------------------ */
#define BT_ERROR_FRAME_FORMAT 0xE0 // 帧格式错误

/* ------------------------------------------------------------------------
 * 成功协议定义
 * ------------------------------------------------------------------------ */
#define BT_NEXT_FRAME 0xC0 // 申请下一帧

/* ------------------------------------------------------------------------
 * 文件传输配置
 * ------------------------------------------------------------------------ */
#define FILE_TRANSFER_CHUNK_SIZE 4096   // 每次传输的数据块大小
#define FILE_MAX_NAME_LENGTH 64         // 文件名最大长度
#define FILE_MAX_SIZE (5 * 1024 * 1024) // 单个文件最大大小 (5MB，支持更大的GIF文件)
#define FILE_TRANSFER_TIMEOUT 2000      // 文件传输超时时间 (毫秒，增加到60秒)

/* ------------------------------------------------------------------------
 * GIF动画播放配置
 * ------------------------------------------------------------------------ */
#define GIF_DEFAULT_FRAME_DELAY 50         // 默认GIF帧延迟 (毫秒)
#define GIF_MAX_WIDTH 64                   // GIF最大宽度
#define GIF_MAX_HEIGHT 32                  // GIF最大高度
#define GIF_STARTUP_FILE "/gifs/kaiji.gif" // 开机播放的GIF文件
#define GIF_STORAGE_PATH "/gifs/"          // GIF文件存储路径
#define GIF_MEMORY_THRESHOLD (100 * 1024)  // 内存模式阈值，超过此大小使用流式播放 (10KB)
#define GIF_STREAM_BUFFER_SIZE 8192        // 流式播放缓冲区大小 (8KB)

/* ------------------------------------------------------------------------
 * 文件系统配置
 * ------------------------------------------------------------------------ */
#define FS_FORMAT_ON_FAIL true // 挂载失败时是否格式化
#define FS_MAX_OPEN_FILES 5    // 最大同时打开文件数

/* ------------------------------------------------------------------------
 * 调试输出配置 - 调整这些值来控制调试信息的输出量
 * 级别说明: 0=无输出, 1=仅错误, 2=警告+错误, 3=信息+警告+错误, 4=调试信息, 5=详细信息
 *
 * 性能优化建议:
 * - 生产环境建议设置为 DEBUG_LEVEL_ERROR (1) 或 DEBUG_LEVEL_NONE (0)
 * - 开发调试时可以设置为 DEBUG_LEVEL_INFO (3) 或更高
 * - 如果遇到蓝牙接收速度慢或rxfull错误，请降低调试级别
 * ------------------------------------------------------------------------ */
// // 开发调试设置
// #define GLOBAL_DEBUG_LEVEL DEBUG_LEVEL_NONE     // 全局调试级别 (默认仅错误)
// #define BT_DEBUG_LEVEL DEBUG_LEVEL_NONE         // 蓝牙调试级别 (默认仅错误)
// #define FILE_DEBUG_LEVEL DEBUG_LEVEL_NONE       // 文件传输调试级别 (默认仅错误)
// #define GIF_DEBUG_LEVEL DEBUG_LEVEL_NONE        // GIF播放调试级别 (默认仅错误)

// #define GLOBAL_DEBUG_LEVEL DEBUG_LEVEL_ERROR
// #define BT_DEBUG_LEVEL DEBUG_LEVEL_ERROR
// #define FILE_DEBUG_LEVEL DEBUG_LEVEL_ERROR
// #define GIF_DEBUG_LEVEL DEBUG_LEVEL_ERROR

// #define GLOBAL_DEBUG_LEVEL DEBUG_LEVEL_WARN
// #define BT_DEBUG_LEVEL DEBUG_LEVEL_WARN
// #define FILE_DEBUG_LEVEL DEBUG_LEVEL_WARN
// #define GIF_DEBUG_LEVEL DEBUG_LEVEL_WARN

#define GLOBAL_DEBUG_LEVEL DEBUG_LEVEL_INFO
#define BT_DEBUG_LEVEL DEBUG_LEVEL_INFO
#define FILE_DEBUG_LEVEL DEBUG_LEVEL_INFO
#define GIF_DEBUG_LEVEL DEBUG_LEVEL_INFO

// #define GLOBAL_DEBUG_LEVEL DEBUG_LEVEL_DEBUG
// #define BT_DEBUG_LEVEL DEBUG_LEVEL_DEBUG
// #define FILE_DEBUG_LEVEL DEBUG_LEVEL_DEUBG
// #define GIF_DEBUG_LEVEL DEBUG_LEVEL_DEBUG

// #define GLOBAL_DEBUG_LEVEL DEBUG_LEVEL_VERBOSE
// #define BT_DEBUG_LEVEL DEBUG_LEVEL_VERBOSE
// #define FILE_DEBUG_LEVEL DEBUG_LEVEL_VERBOSE
// #define GIF_DEBUG_LEVEL DEBUG_LEVEL_VERBOSE

// 快速禁用所有调试输出的开关 - 取消注释下面这行可以完全禁用所有调试输出
// #define DISABLE_ALL_DEBUG

/* ------------------------------------------------------------------------
 * 默认偏移量
 * ------------------------------------------------------------------------ */
#define GIF_DEFAULT_OFFSET_X 0 // 默认GIF显示X偏移量
#define GIF_DEFAULT_OFFSET_Y 0 // 默认GIF显示Y偏移量

/* ------------------------------------------------------------------------
 * 分区显示模式配置（GIF + 文字）
 * ------------------------------------------------------------------------ */
#define SPLIT_GIF_SIZE 32                                 // 分区模式下GIF固定尺寸：32x32
#define SPLIT_GIF_LEFT_X 0                                // GIF在左边时的X坐标
#define SPLIT_GIF_RIGHT_X (SCREEN_WIDTH - SPLIT_GIF_SIZE) // GIF在右边时的X坐标（动态计算）
#define SPLIT_GIF_Y 0                                     // GIF的Y坐标（固定为0）
#define SPLIT_TEXT_LEFT_X SPLIT_GIF_SIZE                  // 文字在左边时的X坐标（GIF右侧）
#define SPLIT_TEXT_RIGHT_X 0                              // 文字在右边时的X坐标（GIF左侧）
#define SPLIT_TEXT_Y 0                                    // 文字的Y坐标（固定为0）
#define SPLIT_TEXT_WIDTH (SCREEN_WIDTH - SPLIT_GIF_SIZE)  // 文字区域宽度（动态计算）
#define SPLIT_TEXT_HEIGHT SCREEN_HEIGHT                   // 文字区域高度（等于屏幕高度）

// GIF位置模式定义
#define GIF_POSITION_LEFT 0  // GIF显示在左边
#define GIF_POSITION_RIGHT 1 // GIF显示在右边

// 分区模式状态定义（五种状态）
#define SPLIT_MODE_DISABLED 0x00         // 退出分区模式
#define SPLIT_MODE_GIF_LEFT_TEXT32 0x01  // GIF左侧 + 32x32文字右侧
#define SPLIT_MODE_GIF_LEFT_TEXT16 0x02  // GIF左侧 + 16x16双行文字右侧
#define SPLIT_MODE_GIF_RIGHT_TEXT32 0x03 // GIF右侧 + 32x32文字左侧
#define SPLIT_MODE_GIF_RIGHT_TEXT16 0x04 // GIF右侧 + 16x16双行文字左侧

// 分区模式数据长度
#define BT_SPLIT_MODE_DATA_LEN 1 // 分区模式命令数据长度（1字节模式）

/* ------------------------------------------------------------------------
 * 列表配置
 * ------------------------------------------------------------------------ */
#define MAX_FILENAME_LEN FILE_MAX_NAME_LENGTH // 文件名最大长度
#define MAX_FILES 16                          // 最大文件数量
#define HEX_BUFFER_LEN 256                    // HEX 数据缓冲区大小

/* ------------------------------------------------------------------------
 * 列表功能数据结构定义
 * ------------------------------------------------------------------------ */
struct ListConfig
{
    uint8_t sequence;         // 序列号1-32
    uint8_t contentFlags;     // bit0=hasGif, bit1=hasText, bit2=isOverlay, bit3=reserved
    char gifFilename[32];     // GIF文件名
    uint8_t fontType;         // 字体类型：0x00=16x16, 0x01=32x32
    uint8_t screenArea;       // 显示区域：0x01=上半屏, 0x02=下半屏, 0x03=全屏
    uint8_t textColor[3];     // RGB文字颜色
    uint8_t bgColor[3];       // RGB背景颜色
    uint8_t effect;           // 特效类型：0x00=静态, 0x01=左滚, 0x02=右滚等
    uint8_t effectSpeed;      // 特效速度：1-10
    uint8_t borderStyle;      // 边框样式
    uint8_t borderColorIndex; // 边框颜色索引
    uint8_t borderEffect;     // 边框效果
    uint8_t borderSpeed;      // 边框速度
    uint8_t gradientMode;     // 渐变模式：0x00=无, 0x01=垂直, 0x02=水平
    uint8_t charCount;        // 字符数量：1-100
    uint8_t fontData[2000];
};

/* ------------------------------------------------------------------------
 * 列表功能全局变量声明（已移至app_list.h）
 * ------------------------------------------------------------------------ */

enum class loopstate
{
    loop_state_gif,
    loop_state_transfer,
    loop_state_clock,
    loop_state_score,
    loop_state_text,
    loop_state_split_display, // 🆕 分区显示：GIF+文字同时显示
    loop_state_background,
    loop_state_list,
    loop_state_overlay,
    loop_state_special
};
#endif // CONFIG_H
