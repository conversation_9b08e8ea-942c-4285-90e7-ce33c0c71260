# 列表功能优化方案

## 1. 统一配置结构设计（条件字段优化版）

```cpp
struct ListConfig {
    uint8_t sequence;        // 序列号（1-32）
    uint8_t contentFlags;    // 内容标志位：bit0=hasGif, bit1=hasText, bit2=isOverlay, bit3=reserved
    uint8_t displayMode;     // 显示模式：0x00=纯GIF, 0x01=纯文本, 0x02=叠层, 0x03=分区
    uint8_t reserved;        // 预留字段

    // === GIF配置区域（仅当hasGif=1时有效） ===
    char gifFilename[32];    // GIF文件名

    // === 文本配置区域（仅当hasText=1时有效） ===
    uint8_t fontType;        // 字体类型：0x00=16x16, 0x01=32x32
    uint8_t screenArea;      // 显示区域：0x01=上半屏, 0x02=下半屏, 0x03=全屏
    uint8_t textColor[3];    // RGB文本颜色
    uint8_t bgColor[3];      // RGB背景颜色（叠层模式下无效）
    uint8_t effect;          // 特效类型：0x00=静态, 0x01=左滚, 0x02=右滚等
    uint8_t effectSpeed;     // 特效速度：1-10
    uint8_t borderStyle;     // 边框样式：0x00=无边框，0x01=实线边框，0x02=点线边框，0x03=角落边框，0x04=彩虹边框
    uint8_t borderColorIndex;// 边框颜色索引：0x00=红色，0x01=绿色，0x02=蓝色，0x03=黄色，0x04=紫色，0x05=青色，0x06=白色
    uint8_t borderEffect;    // 边框效果：0x00=静止显示，0x01=顺时针流动，0x02=逆时针流动，0x03=闪烁效果
    uint8_t borderSpeed;     // 边框速度：1-10（1=最慢，10=最快）
    uint8_t gradientMode;    // 渐变模式：0x00=无, 0x01=垂直, 0x02=水平
    uint8_t charCount;       // 字符数量：1-100
    uint8_t fontData[3200];  // 点阵数据（最大支持100个32x32字符：100×32=3200字节）
};
```

### 内容类型判断逻辑

- `hasGif=0, hasText=1` → 纯文本模式
- `hasGif=1, hasText=0` → 纯 GIF 模式
- `hasGif=1, hasText=1, isOverlay=1` → 叠层模式（GIF 背景+文字前景）

## 2. 单片机端存储管理

```cpp
// 列表配置数组
ListConfig configList[MAX_LIST_ITEMS];  // 最大支持32个列表项
uint8_t currentEditIndex = 1;           // 当前编辑的列表索引（1-32）
uint8_t totalListItems = 0;             // 当前列表项总数
bool isEditMode = false;                // 是否处于编辑模式
```

## 3. APP 端交互流程

### 正常添加流程：

1. APP 发送数据 → 存储到 `configList[currentEditIndex]`
2. APP 点击"保存到列表" → `currentEditIndex++`，切换到下一个位置
3. 重复步骤 1-2，直到添加完所有列表项

### 编辑模式流程：

1. APP 点击"编辑模式" → 发送编辑模式开关命令
2. APP 选择要编辑的列表项 → 发送设置编辑索引命令
3. APP 重新发送配置数据 → 覆盖指定索引的配置
4. APP 点击"退出编辑" → 发送编辑模式关闭命令

## 4. 内容类型自动判断

单片机端根据 `contentFlags` 和数据内容自动判断显示类型：

```cpp
DisplayType getDisplayType(const ListConfig& config) {
    bool hasGif = (config.contentFlags & 0x01) && (strlen(config.gifFilename) > 0);
    bool hasText = (config.contentFlags & 0x02) && (config.charCount > 0);
    bool isOverlay = (config.contentFlags & 0x04);

    if (hasGif && hasText && isOverlay) return OVERLAY_MODE;      // 叠层模式
    else if (hasGif) return GIF_MODE;                           // 纯GIF
    else if (hasText) return TEXT_MODE;                         // 纯文本
    else return EMPTY_MODE;                                     // 空配置
}
```

## 5. 蓝牙协议命令定义

### 5.1 列表管理命令

| 命令码 | 命令名称     | 数据长度 | 说明                           |
| ------ | ------------ | -------- | ------------------------------ |
| 0x40   | 设置编辑索引 | 1 字节   | 设置当前操作的列表索引（1-32） |
| 0x42   | 编辑模式开关 | 1 字节   | 0x00=关闭编辑, 0x01=开启编辑   |
| 0x43   | 开始列表播放 | 0 字节   | 开始播放当前列表               |
| 0x44   | 停止列表播放 | 0 字节   | 停止列表播放                   |
| 0x45   | 清空列表     | 0 字节   | 清空所有列表项                 |
| 0x46   | 删除列表项   | 1 字节   | 删除指定索引的列表项           |

### 5.2 命令详细格式

#### 设置编辑索引 (0x40)

```
AA 55 40 00 01 [索引] 0D 0A
```

- 索引：1-32，指定要操作的列表位置

#### 编辑模式开关 (0x42)

```
AA 55 42 00 01 [模式] 0D 0A
```

- 模式：0x00=关闭编辑, 0x01=开启编辑

#### 开始列表播放 (0x43)

```
AA 55 43 00 00 0D 0A
```

### 5.3 数据存储机制

当设置编辑索引后，所有常规指令的数据都会自动存储到对应的列表项中：

```cpp
// 示例：当前编辑索引为2时
currentEditIndex = 2;

// 收到GIF播放指令 (0x15)
handlePlayGifCommand() {
    // 数据存储到 configList[currentEditIndex]
    strcpy(configList[currentEditIndex].gifFilename, receivedFilename);
    configList[currentEditIndex].contentFlags |= 0x01;  // 设置hasGif标志
}

// 收到文本指令 (0x04)
handleTextCommand() {
    // 数据存储到 configList[currentEditIndex]
    configList[currentEditIndex].charCount = receivedCharCount;
    memcpy(configList[currentEditIndex].fontData, receivedFontData, dataSize);
    configList[currentEditIndex].contentFlags |= 0x02;  // 设置hasText标志
}

// 收到叠层GIF指令 (0x16)
handlePlayGifOverlayCommand() {
    // 同时设置GIF和叠层标志
    strcpy(configList[currentEditIndex].gifFilename, receivedFilename);
    configList[currentEditIndex].contentFlags |= 0x05;  // hasGif + isOverlay
}
```

### 5.4 响应机制

| 命令 | 成功响应                | 失败响应                   |
| ---- | ----------------------- | -------------------------- |
| 0x40 | `0xC0`                  | `ERROR:Invalid index`      |
| 0x42 | `OK:Edit mode [ON/OFF]` | `ERROR:Mode switch failed` |
| 0x43 | `OK:List playing`       | `ERROR:Empty list`         |
| 0x44 | `OK:List stopped`       | `ERROR:Not playing`        |
| 0x45 | `OK:List cleared`       | `ERROR:Clear failed`       |
| 0x46 | `OK:Item deleted`       | `ERROR:Invalid index`      |

## 6. 使用示例

### 示例 1：添加纯 GIF 列表项

```
1. AA 55 40 00 01 01 0D 0A              // 设置编辑索引为1
2. AA 55 15 00 08 demo.gif 0D 0A        // 发送GIF播放指令，数据存入configList[1]
3. AA 55 40 00 01 02 0D 0A              // 切换到编辑索引2
```

### 示例 2：添加叠层模式列表项

```
1. AA 55 40 00 01 02 0D 0A              // 设置编辑索引为2
2. AA 55 16 00 0A background.gif 0D 0A  // 发送叠层GIF指令，设置背景
3. AA 55 04 [文本数据] 0D 0A            // 发送文本指令，设置前景文字
4. AA 55 06 [颜色数据] 0D 0A            // 发送颜色指令，设置文字颜色
```

### 示例 3：编辑已有列表项

```
1. AA 55 42 00 01 01 0D 0A              // 开启编辑模式
2. AA 55 40 00 01 01 0D 0A              // 选择要编辑的列表项（索引1）
3. AA 55 15 00 09 newdemo.gif 0D 0A     // 重新发送GIF指令，覆盖原数据
4. AA 55 42 00 01 00 0D 0A              // 关闭编辑模式
```

### 示例 4：开始播放列表

```
AA 55 43 00 00 0D 0A                   // 开始列表播放
```
