# APP 端列表功能指令发送指南

## 📋 概述

本文档详细说明 APP 端如何向 LED 显示屏发送列表功能相关指令，包括添加列表项、编辑列表、播放控制等操作的完整指令序列。

## 🎯 核心设计理念

- **复用现有指令** - 使用已有的 GIF、文本、颜色等指令
- **索引管理** - 通过 0x40 指令设置当前操作的列表项索引
- **自动存储** - 指令数据自动存储到当前索引的结构体中
- **简化协议** - 无需学习新的复杂指令

## 📊 列表功能专用指令

| 命令码 | 命令名称     | 数据长度 | 说明                           |
| ------ | ------------ | -------- | ------------------------------ |
| 0x40   | 设置编辑索引 | 1 字节   | 设置当前操作的列表索引（1-32） |
| 0x41   | 保存到列表   | 0 字节   | 保存当前配置并切换到下一索引   |
| 0x42   | 编辑模式开关 | 1 字节   | 0x00=关闭编辑, 0x01=开启编辑   |
| 0x43   | 开始列表播放 | 0 字节   | 开始播放当前列表               |
| 0x44   | 停止列表播放 | 0 字节   | 停止列表播放                   |
| 0x45   | 清空列表     | 0 字节   | 清空所有列表项                 |
| 0x46   | 删除列表项   | 1 字节   | 删除指定索引的列表项           |
| 0x47   | 取消编辑     | 0 字节   | 清空临时配置，退出编辑状态     |

## 🔧 常用指令速查表

| 功能     | 指令码 | 说明                     |
| -------- | ------ | ------------------------ |
| 播放 GIF | 0x15   | 普通 GIF 播放            |
| 叠层 GIF | 0x16   | GIF 作为背景（叠层模式） |
| 发送文本 | 0x04   | 文本显示                 |
| 设置颜色 | 0x06   | 文字颜色                 |
| 设置特效 | 0x08   | 滚动特效                 |
| 设置边框 | 0x0A   | 边框样式                 |
| 亮度控制 | 0x07   | 屏幕亮度                 |

## 📱 APP 操作场景指南

### 🎬 场景 1：添加纯 GIF 列表项

**用户操作：** 选择 GIF 文件 → 点击"保存到列表"

**APP 发送序列：**

```
1. AA 55 42 00 01 01 0D 0A              // 开启编辑模式
   响应：OK:Edit mode ON

2. AA 55 15 00 08 demo.gif 0D 0A        // 发送GIF播放指令（默认存储到tempConfig）
   响应：0xC0

3. AA 55 41 00 00 0D 0A                 // 保存到列表（从tempConfig复制到正式列表）
   响应：OK:Saved to list index 1
```

### 📝 场景 2：添加纯文本列表项

**用户操作：** 输入文字 → 设置颜色/特效 → 点击"保存到列表"

**APP 发送序列：**

```
1. AA 55 40 00 01 02 0D 0A              // 设置编辑索引为2
   响应：0xC0

2. AA 55 04 [文本数据] 0D 0A            // 发送文本指令
   响应：0xC0

3. AA 55 06 [颜色数据] 0D 0A            // 发送颜色指令
   响应：0xC0

4. AA 55 08 [特效数据] 0D 0A            // 发送特效指令（可选）
   响应：0xC0

5. AA 55 40 00 01 03 0D 0A              // 切换到索引3
   响应：0xC0
```

### 🎭 场景 3：添加叠层模式（GIF 背景+文字前景）

**用户操作：** 选择背景 GIF → 输入前景文字 → 设置文字颜色 → 点击"保存到列表"

**APP 发送序列：**

```
1. AA 55 40 00 01 03 0D 0A              // 设置编辑索引为3
   响应：0xC0

2. AA 55 16 00 0A background.gif 0D 0A  // 发送叠层GIF指令（背景）
   响应：0xC0

3. AA 55 04 [文本数据] 0D 0A            // 发送文本指令（前景）
   响应：0xC0

4. AA 55 06 [颜色数据] 0D 0A            // 发送文字颜色指令
   响应：0xC0

5. AA 55 40 00 01 04 0D 0A              // 切换到索引4
   响应：0xC0
```

### ✏️ 场景 4：编辑已有列表项

**用户操作：** 点击"编辑模式" → 选择要编辑的项目 → 修改内容 → 保存

**APP 发送序列：**

```
1. AA 55 42 00 01 01 0D 0A              // 开启编辑模式
   响应：OK:Edit mode ON

2. AA 55 40 00 01 02 0D 0A              // 设置编辑第2项（直接编辑configList[1]）
   响应：0xC0

3. AA 55 15 00 0B new_demo.gif 0D 0A    // 发送新的GIF文件（直接覆盖第2项）
   响应：0xC0

4. AA 55 42 00 01 00 0D 0A              // 关闭编辑模式（修改立即生效）
   响应：OK:Edit mode OFF
```

### ▶️ 场景 5：播放列表

**用户操作：** 点击"开始播放列表"

**APP 发送序列：**

```
AA 55 43 00 00 0D 0A                    // 开始列表播放
响应：OK:List playing
```

### ⏹️ 场景 6：停止播放

**用户操作：** 点击"停止播放"

**APP 发送序列：**

```
AA 55 44 00 00 0D 0A                    // 停止列表播放
响应：OK:List stopped
```

### 🗑️ 场景 7：删除特定列表项

**用户操作：** 长按列表项 → 点击"删除"

**APP 发送序列：**

```
AA 55 46 00 01 03 0D 0A                 // 删除第3个列表项
响应：OK:Item deleted
```

### 🧹 场景 8：清空整个列表

**用户操作：** 点击"清空列表" → 确认删除

**APP 发送序列：**

```
AA 55 45 00 00 0D 0A                    // 清空所有列表项
响应：OK:List cleared
```

### ❌ 场景 9：取消编辑（用户退出不保存）

**用户操作：** 编辑过程中点击"取消"或"返回"按钮

**APP 发送序列：**

```
AA 55 47 00 00 0D 0A                    // 取消编辑，清空临时配置
响应：OK:Edit cancelled
```

## 🔍 详细指令格式说明

### 设置编辑索引 (0x40)

```
格式：AA 55 40 00 01 [索引] 0D 0A
索引：01-20=编辑指定项（直接编辑configList[n-1]）
功能：指定编辑已有的列表项，默认情况下数据存储到tempConfig
示例：AA 55 40 00 01 05 0D 0A  // 设置为编辑第5项
```

### 编辑模式开关 (0x42)

```
格式：AA 55 42 00 01 [模式] 0D 0A
模式：00=关闭编辑, 01=开启编辑
示例：AA 55 42 00 01 01 0D 0A  // 开启编辑模式
```

### 删除列表项 (0x46)

```
格式：AA 55 46 00 01 [索引] 0D 0A
索引：01-20 (十六进制)
示例：AA 55 46 00 01 03 0D 0A  // 删除第3项
```

### 保存到列表 (0x41)

```
格式：AA 55 41 00 00 0D 0A
功能：将临时配置保存到下一个可用位置
示例：AA 55 41 00 00 0D 0A  // 保存当前临时配置
```

### 取消编辑 (0x47)

```
格式：AA 55 47 00 00 0D 0A
功能：清空临时配置，取消当前编辑
示例：AA 55 47 00 00 0D 0A  // 取消编辑
```

## ⚠️ 重要注意事项

### 🔄 临时配置机制

1. **临时存储** - 用户编辑时数据先存储到临时配置 `tempConfig`
2. **正式保存** - 只有发送 0x41 指令才将临时配置复制到正式列表
3. **自动递增** - 每次保存成功后，`nextSaveIndex` 自动递增
4. **取消保护** - 用户取消编辑时(0x47)，临时配置被清空，不影响已保存列表

### 📡 指令发送时序

1. **等待响应** - 每个指令发送后必须等待单片机响应
2. **错误处理** - 收到 ERROR 响应时应提示用户并停止操作
3. **超时处理** - 设置 3 秒超时，超时后重发或提示连接异常

### 💾 数据持久化

1. **列表保存** - 列表数据在单片机端自动保存到存储器
2. **断电保护** - 重启后列表数据不会丢失
3. **容量限制** - 最多支持 32 个列表项

### 🎯 最佳实践

#### APP 端状态管理

```javascript
class ListManager {
  constructor() {
    this.nextSaveIndex = 1; // 下一个保存位置索引
    this.isEditMode = false; // 是否处于编辑模式
    this.totalItems = 0; // 列表项总数
    this.tempConfigDirty = false; // 临时配置是否有未保存的修改
  }

  // 开始编辑新项目
  async startEditNewItem() {
    await this.enableEditMode(); // 发送 0x42 开启编辑模式
    this.tempConfigDirty = false;
  }

  // 发送数据到临时配置
  async sendDataToTemp(data) {
    await this.sendItemData(data); // 数据存储到 tempConfig
    this.tempConfigDirty = true;
  }

  // 保存到正式列表
  async saveToList() {
    if (!this.tempConfigDirty) {
      throw new Error("没有需要保存的数据");
    }

    await this.sendSaveCommand(); // 发送 0x41 保存指令
    this.nextSaveIndex++;
    this.totalItems++;
    this.tempConfigDirty = false;
  }

  // 取消编辑
  async cancelEdit() {
    if (this.tempConfigDirty) {
      await this.sendCancelCommand(); // 发送 0x47 取消指令
    }
    await this.disableEditMode(); // 发送 0x42 关闭编辑模式
    this.tempConfigDirty = false;
  }

  // 检查是否有未保存的修改
  hasUnsavedChanges() {
    return this.tempConfigDirty;
  }
}
```

#### 错误处理机制

```javascript
async function sendCommand(command) {
  const maxRetries = 3;
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await sendBluetoothCommand(command);
      if (response.startsWith("ERROR:")) {
        throw new Error(response);
      }
      return response;
    } catch (error) {
      if (i === maxRetries - 1) {
        showError(`发送失败: ${error.message}`);
        return null;
      }
      await delay(1000); // 重试前等待1秒
    }
  }
}
```

## 📈 性能优化建议

1. **批量操作** - 添加多个列表项时，可以连续发送指令，但要等待每个响应
2. **缓存索引** - APP 端缓存当前编辑索引，避免重复发送 0x40 指令
3. **预检查** - 发送前检查文件是否存在，避免无效指令
4. **进度提示** - 长列表添加时显示进度条，提升用户体验

---

## 📞 技术支持

如有疑问或遇到问题，请参考：

- 🔗 [蓝牙 GIF 播放帧格式.md](./蓝牙GIF播放帧格式.md)
- 🔗 [蓝牙文本帧格式.md](./蓝牙文本帧格式.md)
- 🔗 [列表修改方向.md](./列表修改方向.md)
