#include "bluetooth_protocol.h"
#include "BluetoothSerial.h"

extern BluetoothSerial SerialBT;

BluetoothProtocolParser::BluetoothProtocolParser()
{
    dataBuffer = new uint8_t[MAX_DATA_LENGTH];
    errorCount = 0;
    timeoutCheckCounter = 0;
    reset();
}

BluetoothProtocolParser::~BluetoothProtocolParser()
{
    delete[] dataBuffer;
}

void BluetoothProtocolParser::reset()
{
    currentState = ParseState::WAITING_HEADER1;
    command = 0;
    dataLength = 0;
    dataReceived = 0;
    timeoutCheckCounter = 0;
    frameStartTime = millis();
}

bool BluetoothProtocolParser::isFrameComplete() const
{
    return currentState == ParseState::FRAME_COMPLETE;
}

ParseResult BluetoothProtocolParser::parseByte(uint8_t byte, BluetoothFrame &frame)
{
    // 优化：每100字节检查一次超时，减少millis()调用开销
    if (++timeoutCheckCounter >= 100)
    {
        timeoutCheckCounter = 0;
        if (isFrameTimeout())
        {
            errorCount++;
            reset();
            return ParseResult::FRAME_ERROR;
        }
    }

    switch (currentState)
    {
    case ParseState::WAITING_HEADER1:
        if (byte == BT_FRAME_HEADER_1)
        {
            currentState = ParseState::WAITING_HEADER2;
            frameStartTime = millis();
        }
        break;

    case ParseState::WAITING_HEADER2:
        if (byte == BT_FRAME_HEADER_2)
        {
            currentState = ParseState::WAITING_COMMAND;
        }
        else
        {
            errorCount++;
            reset();
            Serial.println("H2");
            return ParseResult::FRAME_ERROR;
        }
        break;

    case ParseState::WAITING_COMMAND:
        if ((byte >= BT_CMD_SET_DIRECTION && byte <= BT_CMD_SET_SPLIT_MODE) ||
            (byte >= BT_CMD_FILE_START && byte <= BT_CMD_PLAY_GIF) ||
            (byte >= BT_CMD_LIST_START && byte <= BT_CMD_LIST_END) ||
            (byte >= BT_CMD_SET_EDIT_INDEX && byte <= BT_CMD_CANCEL_EDIT) ||
            (byte >= BT_CMD_CLOCK_MODE && byte <= BT_CMD_SCORE_MODE) ||
            (byte == BT_CMD_PLAY_GIF_OVERLAY) ||
            (byte == BT_CMD_PLAY_GIF_SPECIAL))
        {
            command = byte;
            currentState = ParseState::WAITING_LENGTH_HIGH;
        }
        else
        {
            errorCount++;
            reset();
            return ParseResult::INVALID_COMMAND;
        }
        break;

    case ParseState::WAITING_LENGTH_HIGH:
        dataLength = (uint16_t)byte << 8;
        currentState = ParseState::WAITING_LENGTH_LOW;
        break;

    case ParseState::WAITING_LENGTH_LOW:
        dataLength |= byte;
        dataReceived = 0;

        if (dataLength > MAX_DATA_LENGTH)
        {
            errorCount++;
            reset();
            return ParseResult::DATA_TOO_LONG;
        }

        if (dataLength == 0)
        {
            currentState = ParseState::WAITING_TAIL1;
        }
        else
        {
            currentState = ParseState::WAITING_DATA;
        }
        break;

    case ParseState::WAITING_DATA:
    {
        dataBuffer[dataReceived++] = byte;

        // int a = dataLength - dataReceived;
        // int b = SerialBT.available();
        // int c = min(a,b);
        // SerialBT.readBytes(dataBuffer+dataReceived, c);
        // dataReceived+=c;

        fileTransferTime_packetloss = millis();
        if (dataReceived >= dataLength)
        {
            currentState = ParseState::WAITING_TAIL1;
        }
    }
    break;

    case ParseState::WAITING_TAIL1:
        if (byte == BT_FRAME_TAIL_1)
        {
            currentState = ParseState::WAITING_TAIL2;
        }
        else
        {
            errorCount++;
            reset();
            Serial.println("T1");
            return ParseResult::FRAME_ERROR;
        }
        break;

    case ParseState::WAITING_TAIL2:
        if (byte == BT_FRAME_TAIL_2)
        {
            // 清理frame的旧数据（如果有）
            if (frame.ownsData && frame.data)
            {
                delete[] frame.data;
            }

            frame.command = command;
            frame.dataLength = dataLength;
            frame.isValid = true;
            frame.timestamp = millis();

            // 创建数据的深拷贝，避免悬空指针问题
            if (dataLength > 0)
            {
                frame.data = new uint8_t[dataLength];
                memcpy(frame.data, dataBuffer, dataLength);
                frame.ownsData = true;
            }
            else
            {
                frame.data = nullptr;
                frame.ownsData = false;
            }

            // 重置转换状态，强制重新转换
            if (frame.convertedData)
            {
                delete[] frame.convertedData;
                frame.convertedData = nullptr;
            }
            frame.isConverted = false;

            currentState = ParseState::FRAME_COMPLETE;
            return ParseResult::FRAME_COMPLETE;
        }
        else
        {
            errorCount++;
            reset();
            Serial.println("T2");
            return ParseResult::FRAME_ERROR;
        }
        break;

    case ParseState::FRAME_COMPLETE:
        reset();
        return parseByte(byte, frame);
    }

    return ParseResult::NEED_MORE_DATA;
}

// 检查帧是否超时
bool BluetoothProtocolParser::isFrameTimeout() const
{
    return (currentState != ParseState::WAITING_HEADER1) &&
           (millis() - frameStartTime > FRAME_TIMEOUT_MS);
}

// 批量解析缓冲区数据
ParseResult BluetoothProtocolParser::parseBuffer(uint8_t *buffer, size_t length,
                                                 BluetoothFrame frames[], size_t maxFrames, size_t &frameCount)
{
    frameCount = 0;
    ParseResult lastResult = ParseResult::NEED_MORE_DATA;

    for (size_t i = 0; i < length && frameCount < maxFrames; i++)
    {
        lastResult = parseByte(buffer[i], frames[frameCount]);
        if (lastResult == ParseResult::FRAME_COMPLETE)
        {
            frameCount++;
            reset(); // 准备解析下一帧
        }
    }

    return lastResult;
}

// 获取状态字符串
String BluetoothProtocolParser::getStateString() const
{
    switch (currentState)
    {
    case ParseState::WAITING_HEADER1:
        return "WAITING_HEADER1";
    case ParseState::WAITING_HEADER2:
        return "WAITING_HEADER2";
    case ParseState::WAITING_COMMAND:
        return "WAITING_COMMAND";
    case ParseState::WAITING_LENGTH_HIGH:
        return "WAITING_LENGTH_HIGH";
    case ParseState::WAITING_LENGTH_LOW:
        return "WAITING_LENGTH_LOW";
    case ParseState::WAITING_DATA:
        return "WAITING_DATA";
    case ParseState::WAITING_TAIL1:
        return "WAITING_TAIL1";
    case ParseState::WAITING_TAIL2:
        return "WAITING_TAIL2";
    case ParseState::FRAME_COMPLETE:
        return "FRAME_COMPLETE";
    default:
        return "UNKNOWN";
    }
}

// 打印调试信息
void BluetoothProtocolParser::printDebugInfo() const
{
    Serial.printf("Parser State: %s\n", getStateString().c_str());
    Serial.printf("Command: 0x%02X\n", command);
    Serial.printf("Data Length: %d\n", dataLength);
    Serial.printf("Data Received: %d\n", dataReceived);
    Serial.printf("Error Count: %d\n", errorCount);
    Serial.printf("Frame Start Time: %d\n", frameStartTime);
}

// BluetoothFrame 方法实现
String BluetoothFrame::getTextData() const
{
    if (!isValid || data == nullptr || dataLength == 0)
        return "";
    return String((char *)data, dataLength);
}

void BluetoothFrame::getColorData(uint8_t &screenArea, uint8_t &target, uint8_t &mode, uint8_t &r, uint8_t &g, uint8_t &b, uint8_t &gradientMode) const
{
    if (!isValid || data == nullptr || dataLength < BT_COLOR_DATA_LEN)
    {
        screenArea = target = mode = r = g = b = gradientMode = 0;
        return;
    }
    screenArea = data[0];   // 屏幕区域：0x01=上半屏，0x02=下半屏，0x03=全屏
    target = data[1];       // 目标：0x01=文本，0x02=背景
    mode = data[2];         // 模式：0x01=固定色，0x02=渐变色
    r = data[3];            // 红色分量
    g = data[4];            // 绿色分量
    b = data[5];            // 蓝色分量
    gradientMode = data[6]; // 渐变模式
}

uint8_t BluetoothFrame::getBrightnessData() const
{
    if (!isValid || data == nullptr || dataLength < BT_BRIGHTNESS_DATA_LEN)
        return 0;
    return data[0];
}

void BluetoothFrame::getEffectData(uint8_t &screenArea, uint8_t &type, uint8_t &speed) const
{
    if (!isValid || data == nullptr || dataLength < BT_EFFECT_DATA_LEN)
    {
        screenArea = type = speed = 0;
        return;
    }
    screenArea = data[0]; // 屏幕区域
    type = data[1];       // 特效类型
    speed = data[2];      // 速度值
}

void BluetoothFrame::getBorderData(uint8_t &style, uint8_t &colorIndex, uint8_t &effect, uint8_t &speed) const
{
    if (!isValid || data == nullptr || dataLength < BT_BORDER_DATA_LEN)
    {
        style = colorIndex = effect = speed = 0;
        return;
    }
    style = data[0];      // 边框样式
    colorIndex = data[1]; // 颜色索引
    effect = data[2];     // 流动效果
    speed = data[3];      // 流动速度
}

bool BluetoothFrame::isValidCommand() const
{
    return isValid && ((command >= BT_CMD_SET_DIRECTION && command <= BT_CMD_SET_SPLIT_MODE) ||
                       (command >= BT_CMD_FILE_START && command <= BT_CMD_PLAY_GIF) ||
                       (command >= BT_CMD_LIST_START && command <= BT_CMD_LIST_END) ||
                       (command >= BT_CMD_CLOCK_MODE && command <= BT_CMD_SCORE_MODE) ||
                       (command == BT_CMD_PLAY_GIF_OVERLAY) ||
                       (command == BT_CMD_PLAY_GIF_SPECIAL));
}

// 转换8位数据为16位字体数据 (高性能版本)
const uint16_t *BluetoothFrame::getFontData16x16(uint8_t &screenArea, int &charCount) const
{
    // 增强的安全检查
    if (!isValid || !data || dataLength < 1)
    {
        screenArea = 0;
        charCount = 0;
        return nullptr;
    }

    screenArea = data[0];                // 第一个字节是屏幕区域
    int fontDataLength = dataLength - 1; // 减去屏幕区域字节
    charCount = fontDataLength / 32;     // 每个16x16字符32字节

    if (charCount == 0 || fontDataLength < 0)
    {
        charCount = 0;
        return nullptr;
    }

    // 如果还没转换，进行转换
    if (!isConverted && fontDataLength > 0)
    {
        // 确保数据长度是偶数（每2个字节组成1个uint16_t）
        int uint16Count = fontDataLength / 2;
        if (uint16Count > 0)
        {
            convertedData = new uint16_t[uint16Count];

            // 高性能转换：按字节顺序组合 0x12,0x34 -> 0x1234
            for (int i = 0; i < fontDataLength - 1; i += 2)
            {
                convertedData[i / 2] = ((uint16_t)data[1 + i] << 8) | data[1 + i + 1]; // data[1+i]是高字节，data[1+i+1]是低字节
            }
            isConverted = true;
        }
    }

    return convertedData;
}

// 转换8位数据为32x32字体数据
const uint16_t *BluetoothFrame::getFontData32x32(uint8_t &screenArea, int &charCount) const
{
    // 增强的安全检查
    if (!isValid || !data || dataLength < 1)
    {
        screenArea = 0;
        charCount = 0;
        return nullptr;
    }

    screenArea = data[0];                // 第一个字节是屏幕区域
    int fontDataLength = dataLength - 1; // 减去屏幕区域字节
    charCount = fontDataLength / 128;    // 每个32x32字符128字节

    if (charCount == 0 || fontDataLength < 0)
    {
        charCount = 0;
        return nullptr;
    }

    // 如果还没转换，进行转换
    if (!isConverted && fontDataLength > 0)
    {
        // 确保数据长度是偶数（每2个字节组成1个uint16_t）
        int uint16Count = fontDataLength / 2;
        if (uint16Count > 0)
        {
            convertedData = new uint16_t[uint16Count];

            // 高性能转换：按字节顺序组合 0x12,0x34 -> 0x1234
            for (int i = 0; i < fontDataLength - 1; i += 2)
            {
                convertedData[i / 2] = ((uint16_t)data[1 + i] << 8) | data[1 + i + 1]; // data[1+i]是高字节，data[1+i+1]是低字节
            }
            isConverted = true;
        }
    }

    return convertedData;
}

void BluetoothProtocolParser::Error_reset()
{
    currentState = ParseState::WAITING_HEADER1;
    command = 0;
    dataLength = 0;
    dataReceived = 0;
    timeoutCheckCounter = 0;
    frameStartTime = millis();
    memset(dataBuffer, 0, MAX_DATA_LENGTH);
}

// 解析文件开始命令数据
void BluetoothFrame::getFileStartData(String &filename, uint32_t &fileSize) const
{
    if (!isValid || data == nullptr || dataLength < 5)
    {
        filename = "";
        fileSize = 0;
        return;
    }

    // 前4字节是文件大小（小端序）
    fileSize = data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24);

    // 剩余字节是文件名
    if (dataLength > 4)
    {
        filename = String((char *)(data + 4), dataLength - 4);
    }
    else
    {
        filename = "";
    }
}

// 解析删除文件命令数据
String BluetoothFrame::getFileDeleteData() const
{
    if (!isValid || data == nullptr || dataLength == 0)
        return "";
    return String((char *)data, dataLength);
}

// 解析播放GIF命令数据
String BluetoothFrame::getPlayGifData() const
{
    if (!isValid || data == nullptr || dataLength == 0)
        return "";
    return String((char *)data, dataLength);
}

// 解析分区模式命令数据
uint8_t BluetoothFrame::getSplitModeData() const
{
    if (!isValid || data == nullptr || dataLength < BT_SPLIT_MODE_DATA_LEN)
        return SPLIT_MODE_DISABLED; // 默认返回禁用状态
    return data[0];
}

// 计分模式文本数据解析（包含RGB颜色） - 修正版本
void BluetoothFrame::getScoreModeTextDataWithColor(uint8_t &region, uint8_t &colorR, uint8_t &colorG, uint8_t &colorB,
                                                   uint8_t *textData, uint16_t &textDataLength) const
{
    if (!isValid || data == nullptr || dataLength < (BT_SCORE_TEXT_DATA_LEN_MIN) || textData == nullptr)
    {
        region = colorR = colorG = colorB = 0;
        textDataLength = 0;
        return;
    }

    region = data[0]; // 区域选择 (0x00=左上, 0x02=右上)
    colorR = data[1]; // RGB颜色R分量
    colorG = data[2]; // RGB颜色G分量
    colorB = data[3]; // RGB颜色B分量

    // 计算实际文本数据长度（总数据长度 - 区域选择1字节 - RGB颜色3字节）
    textDataLength = dataLength - 4;

    // 限制最大长度为320字节（10个字符）
    if (textDataLength > BT_SCORE_MAX_CHARS * BT_SCORE_CHAR_BYTES)
    {
        textDataLength = BT_SCORE_MAX_CHARS * BT_SCORE_CHAR_BYTES;
    }

    // 从第4字节开始复制文本点阵数据
    memcpy(textData, &data[4], textDataLength);
}

// 计分模式数字数据解析 - 严格按照协议要求：数字区域数据包格式
void BluetoothFrame::getScoreModeNumData(uint8_t &region, uint8_t &colorR, uint8_t &colorG, uint8_t &colorB,
                                         uint16_t &initValue, uint8_t &operation, uint16_t &operValue) const
{
    if (!isValid || data == nullptr || dataLength < BT_SCORE_NUM_DATA_LEN)
    {
        region = colorR = colorG = colorB = operation = 0;
        initValue = operValue = 0;
        return;
    }

    // 严格按照协议要求解析数字区域数据包：
    // 包头（AA 55）+ 命令编号（21）+ 两字节数据长度 +
    // 区域选择（0x01=左下，0x03=右下）+
    // 数字颜色（RGB888）+
    // 设置数字初始值（0~999对应十六进制数据）+
    // 加减选择（0x00=加，0x01=减）+
    // 特定数值（0~999对应十六进制）+
    // 包尾（0D 0A）

    region = data[0]; // 区域选择 (0x01=左下, 0x03=右下)
    colorR = data[1]; // 数字颜色R (RGB888第1字节)
    colorG = data[2]; // 数字颜色G (RGB888第2字节)
    colorB = data[3]; // 数字颜色B (RGB888第3字节)

    // 设置数字初始值（0~999对应十六进制数据）- 按协议要求，应该是两字节
    // 根据常见协议惯例，采用大端序（先高位后低位）
    initValue = (data[4] << 8) | data[5]; // 两字节初始值，data[4]高字节，data[5]低字节

    operation = data[6]; // 加减选择 (0x00=加, 0x01=减)

    // 特定数值（0~999对应十六进制）- 按协议要求，应该是两字节
    operValue = (data[7] << 8) | data[8]; // 两字节特定数值，data[7]高字节，data[8]低字节

    // 注释掉旧逻辑（实际上逻辑是正确的，保持不变）：
    // initValue = (data[4] << 8) | data[5];  // 旧逻辑正确
    // operValue = (data[7] << 8) | data[8];  // 旧逻辑正确
}

// 时钟模式数据解析 - 严格按照协议要求：时钟模式蓝牙数据包格式
void BluetoothFrame::getClockModeData(uint8_t &screenMode, uint8_t &gifSelect,
                                      uint8_t &weekdayR, uint8_t &weekdayG, uint8_t &weekdayB,
                                      uint8_t &timeR, uint8_t &timeG, uint8_t &timeB,
                                      uint8_t &hour, uint8_t &minute, uint8_t &weekday, uint8_t &language) const
{
    if (!isValid || data == nullptr || dataLength < BT_CLOCK_DATA_LEN)
    {
        screenMode = gifSelect = weekdayR = weekdayG = weekdayB = 0;
        timeR = timeG = timeB = hour = minute = weekday = language = 0;
        return;
    }

    // 严格按照协议要求解析时钟模式数据包：
    // 包头（AA 55）+ 命令编号（20）+ 两字节数据长度 +
    // 大小屏模式选择（0x00=小屏，0x01=大屏）+
    // gif选择（0x00~0x07，对应0到7编号的gif文件名）+
    // 星期字体颜色（RGB888）+
    // 时间字体颜色（RGB888）+
    // 时钟数字（0x00~0x17对应0~23）+
    // 分钟数字（0x00~0x3B对应0~59）+
    // 星期选择（0x00~0x06对应周日到周六）+
    // 语言选择（0x00=中文，0x01=英文）+
    // 包尾（0D 0A）

    screenMode = data[0]; // 大小屏模式选择 (0x00=小屏, 0x01=大屏)
    gifSelect = data[1];  // gif选择 (0x00~0x07，对应0到7编号的gif文件名)
    weekdayR = data[2];   // 星期字体颜色R (RGB888第1字节)
    weekdayG = data[3];   // 星期字体颜色G (RGB888第2字节)
    weekdayB = data[4];   // 星期字体颜色B (RGB888第3字节)
    timeR = data[5];      // 时间字体颜色R (RGB888第1字节)
    timeG = data[6];      // 时间字体颜色G (RGB888第2字节)
    timeB = data[7];      // 时间字体颜色B (RGB888第3字节)
    hour = data[8];       // 时钟数字 (0x00~0x17对应0~23)
    minute = data[9];     // 分钟数字 (0x00~0x3B对应0~59)
    weekday = data[10];   // 星期选择 (0x00~0x06对应周日到周六)
    language = data[11];  // 语言选择 (0x00=中文，0x01=英文)
}
