#ifndef APP_LIST_H
#define APP_LIST_H

#include "config.h"
#include "bluetooth_protocol.h"
#include "debug_utils.h"
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "file_handler.h"
#include "gif_player.h"

extern char **file_list; // 保持向后兼容性

// 新的列表功能全局变量声明
extern uint8_t nextSaveIndex;            // 下一个保存位置的索引（1-32）
extern uint8_t editingIndex;             // 当前正在编辑的索引（0=新建模式，1-32=编辑指定项）
extern bool isListEditMode;              // 是否处于列表编辑模式
extern ListConfig tempConfig;            // 临时配置（用户编辑时的缓存）
extern ListConfig configList[MAX_FILES]; // 列表配置数组

void initFileList();
void freeFileList();
void addFileToList(const char *filename);
void printFileList();
int processFileList();
bool play_list_GIF();
void updateGIF_FOR_list();

// 新的列表功能函数声明
void initListConfig();                     // 初始化列表配置
void printListConfig();                    // 打印列表配置内容
void printSingleListConfig(uint8_t index); // 打印单个列表配置

enum list_mode
{
    LIST_NO_MODE = 0,
    LIST_GIF_MODE,
    LIST_TEXT_MODE,
    LIST_UNKNOW
};

#endif // __LIST_H