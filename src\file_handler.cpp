#include "file_handler.h"
#include "debug_utils.h"
#include "retransmission_manager.h"
#include "app_list.h" // 添加列表功能支持
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

extern RetransmissionManager retransManager;

// 文件传输相关变量定义
bool fileTransferActive = false;
String currentFileName = "";
File currentFile;
uint32_t expectedFileSize = 0;
uint32_t receivedFileSize = 0;
unsigned long fileTransferStartTime = 0;
unsigned long fileTransferTime_Every = 0;
bool is_file_transfer_for_bt_parse = false; // 是否为文件传输模式
uint32_t fileTransferTime_packetloss = 0;

// 双缓冲区实现 - 解决25ms阻塞问题
static uint8_t writeBuffer1[4096];            // 缓冲区1
static uint8_t writeBuffer2[4096];            // 缓冲区2
static uint8_t *currentBuffer = writeBuffer1; // 当前接收缓冲区
static uint8_t *writeBuffer = nullptr;        // 当前写入缓冲区
static size_t bufferIndex = 0;
static bool isWriting = false; // 写入状态标志
static uint32_t responseCounter = 0;
static SemaphoreHandle_t bufferMutex = nullptr; // 缓冲区互斥锁
static TaskHandle_t writeTaskHandle = nullptr;  // 写入任务句柄

// 全局错误标志
static volatile bool writeError = false;
static volatile size_t lastWriteResult = 0;

// 异步写入任务
void writeTask(void *parameter)
{
    while (true)
    {
        // 等待写入信号
        ulTaskNotifyTake(pdTRUE, portMAX_DELAY);

        if (writeBuffer && currentFile)
        {
            // 执行实际的flash写入
            size_t written = currentFile.write(writeBuffer, 4096);

            // 简单的错误记录，避免复杂操作
            if (written != 4096)
            {
                writeError = true;
                lastWriteResult = written;
            }
            else
            {
                writeError = false;
            }

            // 获取互斥锁并更新状态
            if (xSemaphoreTake(bufferMutex, pdMS_TO_TICKS(50)) == pdTRUE)
            {
                isWriting = false;
                writeBuffer = nullptr;
                xSemaphoreGive(bufferMutex);
            }
        }
    }
}

// 初始化双缓冲区系统
bool initDoubleBuffer()
{
    // 创建互斥锁
    bufferMutex = xSemaphoreCreateMutex();
    if (!bufferMutex)
    {
        FILE_ERROR("Failed to create buffer mutex");
        return false;
    }

    // 创建写入任务
    BaseType_t result = xTaskCreate(
        writeTask,       // 任务函数
        "WriteTask",     // 任务名称
        4096,            // 栈大小 (增加到4KB)
        nullptr,         // 参数
        1,               // 优先级 (降低优先级避免抢占主任务)
        &writeTaskHandle // 任务句柄
    );

    if (result != pdPASS)
    {
        FILE_ERROR("Failed to create write task");
        vSemaphoreDelete(bufferMutex);
        return false;
    }

    FILE_INFO("Double buffer system initialized");
    return true;
}

// 初始化LittleFS文件系统
bool initLittleFS()
{
    if (!LittleFS.begin(FS_FORMAT_ON_FAIL))
    {
        FILE_ERROR("LittleFS mount failed");
        return false;
    }

    FILE_INFO("LittleFS initialized successfully");

    // 初始化双缓冲区系统
    if (!initDoubleBuffer())
    {
        FILE_ERROR("Double buffer initialization failed");
        return false;
    }

    // 列出文件系统中的文件 - 只在调试模式下显示
    if (FILE_DEBUG_LEVEL >= DEBUG_LEVEL_DEBUG)
    {
        File root = LittleFS.open("/");
        File file = root.openNextFile();
        FILE_DEBUG("Files in LittleFS:");
        while (file)
        {
            FILE_DEBUG("  %s (%d bytes)", file.name(), file.size());
            file = root.openNextFile();
        }
    }

    return true;
}

// 创建gifs目录
bool createGifsDirectory()
{
    if (!LittleFS.exists(GIF_STORAGE_PATH))
    {
        if (LittleFS.mkdir(GIF_STORAGE_PATH))
        {
            FILE_INFO("Created gifs directory successfully");
            return true;
        }
        else
        {
            FILE_ERROR("Failed to create gifs directory");
            return false;
        }
    }
    return true;
}

// 发送响应消息
void sendResponse(uint8_t command, const String &message)
{
    // // 构造响应帧: 帧头 + 命令 + 长度 + 数据 + 帧尾
    // SerialBT.write(BT_FRAME_HEADER_1);
    // SerialBT.write(BT_FRAME_HEADER_2);
    // SerialBT.write(command | 0x80); // 响应命令 = 原命令 | 0x80

    // uint16_t len = message.length();
    // SerialBT.write((len >> 8) & 0xFF);
    // SerialBT.write(len & 0xFF);

    // if (len > 0) {
    //     SerialBT.write((uint8_t*)message.c_str(), len);
    // }

    // SerialBT.write(BT_FRAME_TAIL_1);
    // SerialBT.write(BT_FRAME_TAIL_2);
}

// 中止文件传输
void abortFileTransfer()
{
    if (fileTransferActive)
    {
        // 等待异步写入完成
        while (isWriting)
        {
            vTaskDelay(pdMS_TO_TICKS(1));
        }

        if (currentFile)
        {
            currentFile.close();
            // 删除未完成的文件
            LittleFS.remove(currentFileName);
        }

        // 重置双缓冲区状态
        if (xSemaphoreTake(bufferMutex, pdMS_TO_TICKS(100)) == pdTRUE)
        {
            currentBuffer = writeBuffer1;
            writeBuffer = nullptr;
            bufferIndex = 0;
            isWriting = false;
            responseCounter = 0;
            xSemaphoreGive(bufferMutex);
        }

        fileTransferActive = false;
        currentFileName = "";
        expectedFileSize = 0;
        receivedFileSize = 0;

        Serial.println("=====================================");
        Serial.println("File transfer aborted");
        Serial.println("=== EXITING FILE TRANSFER MODE ===");
        Serial.println("- Resuming normal operations");
        Serial.println("- GIF animation resumed");
        Serial.println("=====================================");
    }
}

// 处理文件开始命令
void handleFileStartCommand(const BluetoothFrame &frame)
{
    String filename;
    uint32_t fileSize;
    frame.getFileStartData(filename, fileSize);

    FILE_INFO("Starting file reception: %s, Size: %d bytes", filename.c_str(), fileSize);

    // 检查是否已有文件传输在进行
    if (fileTransferActive)
    {
        FILE_ERROR("File transfer already in progress");
        return;
    }

    // 检查文件大小
    if (fileSize > FILE_MAX_SIZE)
    {
        FILE_ERROR("File too large (%d > %d)", fileSize, FILE_MAX_SIZE);
        return;
    }

    // 检查文件名
    if (filename.length() == 0 || filename.length() > FILE_MAX_NAME_LENGTH)
    {
        FILE_ERROR("Invalid filename");
        return;
    }

    // 构造完整文件路径
    currentFileName = GIF_STORAGE_PATH + filename;

    // 创建文件
    currentFile = LittleFS.open(currentFileName, "w");
    if (!currentFile)
    {
        FILE_ERROR("Cannot create file %s", currentFileName.c_str());
        return;
    }

    // 初始化传输状态
    fileTransferActive = true;
    expectedFileSize = fileSize;
    receivedFileSize = 0;
    fileTransferStartTime = millis();
    fileTransferTime_Every = millis();

    // 检查双缓冲区系统是否已初始化
    if (!bufferMutex || !writeTaskHandle)
    {
        FILE_ERROR("Double buffer system not initialized");
        return;
    }

    // 初始化双缓冲区状态
    if (xSemaphoreTake(bufferMutex, pdMS_TO_TICKS(100)) == pdTRUE)
    {
        currentBuffer = writeBuffer1;
        writeBuffer = nullptr;
        bufferIndex = 0;
        isWriting = false;
        writeError = false;
        responseCounter = 0;
        xSemaphoreGive(bufferMutex);
    }
    else
    {
        FILE_ERROR("Failed to acquire mutex during initialization");
        return;
    }

    bool is_file_transfer_for_bt_parse = true;
    Serial.println("\nFile transfer started");
    // retransManager.sendWithRetransmission(BT_NEXT_FRAME);
    SerialBT.write(BT_NEXT_FRAME);
}

// 处理文件数据命令
void handleFileDataCommand(const BluetoothFrame &frame)
{
    // 检查是否有活动的文件传输
    if (!fileTransferActive)
    {
        FILE_ERROR("No active file transfer");
        return;
    }

    // 检查文件是否打开
    if (!currentFile)
    {
        FILE_ERROR("File not open");
        abortFileTransfer();
        return;
    }

    // 检查数据长度
    if (frame.dataLength == 0)
    {
        FILE_ERROR("Data length is 0");
        return;
    }

    // 检查是否超出预期大小
    if (receivedFileSize + frame.dataLength > expectedFileSize)
    {
        FILE_ERROR("Data exceeds expected size (%d + %d > %d)",
                   receivedFileSize, frame.dataLength, expectedFileSize);
        abortFileTransfer();
        return;
    }

    // 双缓冲区数据处理
    if (xSemaphoreTake(bufferMutex, pdMS_TO_TICKS(10)) == pdTRUE)
    {
        // 将数据复制到当前接收缓冲区（快速内存操作，无阻塞）
        memcpy(currentBuffer + bufferIndex, frame.data, frame.dataLength);
        bufferIndex += frame.dataLength;

        // 检查缓冲区是否已满
        if (bufferIndex >= 4096)
        {
            // 尝试切换缓冲区
            if (!isWriting)
            {
                // 立即切换缓冲区
                writeBuffer = currentBuffer;
                currentBuffer = (currentBuffer == writeBuffer1) ? writeBuffer2 : writeBuffer1;
                bufferIndex = 0;
                isWriting = true;

                // 通知写入任务开始异步写入
                xTaskNotifyGive(writeTaskHandle);

                // FILE_DEBUG("Buffer switched, async write started");

                // 检查上次写入是否有错误
                if (writeError)
                {
                    FILE_ERROR("Previous async write failed (written %d, expected 4096)", lastWriteResult);
                    writeError = false;
                    xSemaphoreGive(bufferMutex);
                    abortFileTransfer();
                    return;
                }
            }
            else
            {
                // 写入任务忙碌，等待或使用同步写入
                FILE_WARN("Write task busy, falling back to sync write");
                size_t written = currentFile.write(currentBuffer, bufferIndex);
                if (written != bufferIndex)
                {
                    FILE_ERROR("Sync write failed (written %d, expected %d)", written, bufferIndex);
                    xSemaphoreGive(bufferMutex);
                    abortFileTransfer();
                    return;
                }
                bufferIndex = 0;
            }
        }

        xSemaphoreGive(bufferMutex);
    }
    else
    {
        FILE_ERROR("Failed to acquire buffer mutex");
        abortFileTransfer();
        return;
    }

    // retransManager.sendWithRetransmission(BT_NEXT_FRAME);
    SerialBT.write(BT_NEXT_FRAME);

    receivedFileSize += frame.dataLength;
    fileTransferTime_Every = millis(); // 更新超时时间
}

// 处理文件结束命令
void handleFileEndCommand(const BluetoothFrame &frame)
{
    if (!fileTransferActive)
    {
        Serial.println("Error: No active file transfer");
        return;
    }

    // 检查文件大小是否匹配
    if (receivedFileSize != expectedFileSize)
    {
        Serial.printf("Error: File size mismatch (received %d, expected %d)\n",
                      receivedFileSize, expectedFileSize);
        abortFileTransfer();
        return;
    }

    // 等待异步写入完成
    while (isWriting)
    {
        vTaskDelay(pdMS_TO_TICKS(1));
    }

    // 写入剩余缓冲区数据
    if (xSemaphoreTake(bufferMutex, pdMS_TO_TICKS(100)) == pdTRUE)
    {
        if (bufferIndex > 0)
        {
            size_t written = currentFile.write(currentBuffer, bufferIndex);
            if (written != bufferIndex)
            {
                FILE_ERROR("Final buffer write failed (written %d, expected %d)", written, bufferIndex);
                xSemaphoreGive(bufferMutex);
                abortFileTransfer();
                return;
            }
            bufferIndex = 0;
        }
        xSemaphoreGive(bufferMutex);
    }

    // 最终flush确保数据写入flash
    currentFile.flush();

    // 关闭文件
    currentFile.close();

    // 计算传输统计信息
    unsigned long transferTime = millis() - fileTransferStartTime;
    float transferSpeed = (receivedFileSize / 1024.0) / (transferTime / 1000.0); // KB/s

    // 显示文件传输完成提示
    Serial.printf("[FILE] Transfer Complete: %s (%d bytes)\n", currentFileName.c_str(), receivedFileSize);
    Serial.printf("Transfer time: %lu ms (%.2f seconds)\n", transferTime, transferTime / 1000.0);
    Serial.printf("Transfer speed: %.2f KB/s\n", transferSpeed);

    // 重置传输状态 - 退出高性能模式
    fileTransferActive = false;
    String savedFileName = currentFileName;
    currentFileName = "";
    expectedFileSize = 0;
    receivedFileSize = 0;

    // retransManager.sendWithRetransmission(BT_NEXT_FRAME);
    SerialBT.write(BT_NEXT_FRAME);
    bool is_file_transfer_for_bt_parse = false;

    // 如果是GIF文件，可以自动播放
    if (savedFileName.endsWith(".gif"))
    {
        // Serial.println("GIF file detected, can be played using play command");
    }
}

// 处理文件列表命令
void handleFileListCommand(const BluetoothFrame &frame)
{
    Serial.println("Getting file list");
    sendFileList();
}

// 发送文件列表
void sendFileList()
{
    String fileList = "";
    File root = LittleFS.open(GIF_STORAGE_PATH);

    if (!root)
    {
        return;
    }

    File file = root.openNextFile();
    while (file)
    {
        if (!file.isDirectory())
        {
            if (fileList.length() > 0)
            {
                fileList += ";";
            }
            fileList += file.name();
            fileList += ":";
            fileList += String(file.size());
        }
        file = root.openNextFile();
    }

    if (fileList.length() == 0)
    {
        fileList = "No files found";
    }

    Serial.printf("File list: %s\n", fileList.c_str());
}

// 处理删除文件命令
void handleFileDeleteCommand(const BluetoothFrame &frame)
{
    String filename = frame.getFileDeleteData();

    if (filename.length() == 0)
    {
        Serial.println("Error: Empty filename");
        return;
    }

    String fullPath = GIF_STORAGE_PATH + filename;

    if (LittleFS.exists(fullPath))
    {
        if (LittleFS.remove(fullPath))
        {
            Serial.printf("File deleted successfully: %s\n", filename.c_str());
        }
        else
        {
            Serial.printf("File deletion failed: %s\n", filename.c_str());
        }
    }
    else
    {
        Serial.printf("File not found: %s\n", filename.c_str());
    }
}

// 处理播放GIF命令
void handlePlayGifCommand(const BluetoothFrame &frame)
{
    String filename = frame.getPlayGifData();

    if (filename.length() == 0)
    {
        Serial.println("Error: Empty filename");
        return;
    }

    // 列表编辑模式：数据重定向存储
    if (isListEditMode)
    {
        if (editingIndex == 0)
        {
            // 默认模式：存储到tempConfig
            strncpy(tempConfig.gifFilename, filename.c_str(), 31);
            tempConfig.gifFilename[31] = '\0';
            tempConfig.contentFlags |= 0x01; // 设置hasGif标志
            Serial.printf("✅ GIF存储到临时配置: %s\n", filename.c_str());
        }
        else
        {
            // 编辑指定项：存储到configList[editingIndex-1]
            strncpy(configList[editingIndex - 1].gifFilename, filename.c_str(), 31);
            configList[editingIndex - 1].gifFilename[31] = '\0';
            configList[editingIndex - 1].contentFlags |= 0x01; // 设置hasGif标志
            Serial.printf("✅ GIF存储到列表索引%d: %s\n", editingIndex, filename.c_str());
            printSingleListConfig(editingIndex); // 调试输出当前配置
        }
        SerialBT.write(0xC0); // 发送成功响应
        return;
    }

    // 原有播放逻辑保持不变
    String fullPath = GIF_STORAGE_PATH + filename;

    if (!LittleFS.exists(fullPath))
    {
        Serial.printf("File not found: %s\n", filename.c_str());
        return;
    }

    // 调用gif_player模块的智能播放函数

    if (playGIFAuto(fullPath.c_str()))
    {
        Serial.printf("Started playing GIF: %s\n", filename.c_str());
    }
    else
    {
        Serial.printf("Failed to play GIF: %s\n", filename.c_str());
    }

    SerialBT.write(BT_NEXT_FRAME);
}

bool check_packetloss()
{
    if (millis() - fileTransferTime_packetloss > 300)
    {
        fileTransferTime_packetloss = millis();
        return true;
    }
    return false;
}
